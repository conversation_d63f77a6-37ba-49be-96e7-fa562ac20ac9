import { Flex, FlexProps, Text } from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';

import { getImageBySystem } from 'helpers/layout/getImageBySystem';

import Logo from 'components/Autenticacao/Logo';

import { loginBackgroundImgs } from 'constants/enum/enumsImgSistemas';

interface BackgroundProps extends FlexProps {
  children: React.ReactNode;
}

function preload(src: string) {
  return new Promise<void>((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = () => reject();
    img.src = src;
  });
}

export const LoginBackgroundFomer = ({
  children,
  ...rest
}: BackgroundProps) => {
  const url = getImageBySystem(loginBackgroundImgs);
  const [bgReady, setBgReady] = useState(false);

  useEffect(() => {
    setBgReady(false);
    preload(url).finally(() => setBgReady(true));
  }, [url]);
  return (
    <Flex
      position="relative"
      h="100%"
      w="full"
      minH="calc(var(--vh-autenticacao, 1vh) * 100)"
      justifyContent="center"
      alignItems="center"
      bgImage={
        bgReady
          ? `url(${url})`
          : 'linear-gradient(180deg, #5D4A65 0%, #5C435B 8%, #884370 16%, #BE4382 24%, #BA4382 28%, #954382 38%, #784381 47%, #5D4369 62%, #4F4356 82%, #564360 100%)'
      }
      bgPosition="center"
      bgRepeat="no-repeat"
      bgSize="cover"
      px={14}
      {...rest}
    >
      <Flex
        flexDirection="column"
        justifyContent="center"
        h="full"
        minH="calc(var(--vh-autenticacao, 1vh) * 100)"
        w={{ base: '265px', sm: '300px', xl: '350px' }}
        zIndex="docked"
        pt={{ base: '0', sm: '8', xl: '8' }}
        pb={{ base: '4', sm: '8', xl: '8' }}
      >
        <Logo />
        {children}
      </Flex>
      <Flex position="absolute" w="full" justifyContent="center" bottom="0">
        <Text color="purple.100">v{import.meta.env.VITE_APP_VERSION}</Text>
      </Flex>
    </Flex>
  );
};
